import 'package:dio/dio.dart';
import 'package:dio_smart_retry/dio_smart_retry.dart';
import 'package:flutter_audio_room/shared/data/remote/retry_config.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('RetryConfig', () {
    test('should create default strategy correctly', () {
      final config = RetryConfig.fromStrategy(RetryStrategy.defaultStrategy);

      expect(config.retries, 3);
      expect(config.retryDelays.length, 3);
      expect(config.retryDelays[0], const Duration(seconds: 1));
      expect(config.retryDelays[1], const Duration(seconds: 2));
      expect(config.retryDelays[2], const Duration(seconds: 4));
      expect(config.enableLogging, true);
    });

    test('should create fast strategy correctly', () {
      final config = RetryConfig.fromStrategy(RetryStrategy.fastStrategy);

      expect(config.retries, 2);
      expect(config.retryDelays.length, 2);
      expect(config.retryDelays[0], const Duration(milliseconds: 500));
      expect(config.retryDelays[1], const Duration(seconds: 1));
      expect(config.enableLogging, true);
    });

    test('should create conservative strategy correctly', () {
      final config =
          RetryConfig.fromStrategy(RetryStrategy.conservativeStrategy);

      expect(config.retries, 5);
      expect(config.retryDelays.length, 5);
      expect(config.retryDelays[0], const Duration(seconds: 1));
      expect(config.retryDelays[1], const Duration(seconds: 2));
      expect(config.retryDelays[2], const Duration(seconds: 4));
      expect(config.retryDelays[3], const Duration(seconds: 8));
      expect(config.retryDelays[4], const Duration(seconds: 16));
      expect(config.enableLogging, true);
    });

    test('should create no retry strategy correctly', () {
      final config = RetryConfig.fromStrategy(RetryStrategy.noRetry);

      expect(config.retries, 0);
      expect(config.retryDelays.isEmpty, true);
      expect(config.enableLogging, false);
    });

    test('should create custom config correctly', () {
      const config = RetryConfig(
        retries: 4,
        retryDelays: [
          Duration(seconds: 1),
          Duration(seconds: 3),
          Duration(seconds: 5),
          Duration(seconds: 7),
        ],
        retryableExtraStatuses: {408, 423},
        enableLogging: true,
      );

      expect(config.retries, 4);
      expect(config.retryDelays.length, 4);
      expect(config.retryableExtraStatuses, {408, 423});
      expect(config.enableLogging, true);
    });

    test('should create interceptor correctly', () {
      final dio = Dio();
      final config = RetryConfig.fromStrategy(RetryStrategy.defaultStrategy);

      final interceptor = config.createInterceptor(dio);

      expect(interceptor, isA<RetryInterceptor>());
    });

    test('should create empty interceptor for no retry strategy', () {
      final dio = Dio();
      final config = RetryConfig.fromStrategy(RetryStrategy.noRetry);

      final interceptor = config.createInterceptor(dio);

      expect(interceptor, isA<RetryInterceptor>());
    });
  });

  group('RetryConfig Integration', () {
    test('should create interceptor with correct configuration', () {
      final dio = Dio();
      final config = RetryConfig.fromStrategy(RetryStrategy.defaultStrategy);

      final interceptor = config.createInterceptor(dio);

      expect(interceptor, isNotNull);
      expect(interceptor, isA<RetryInterceptor>());
    });

    test('should handle no retry strategy correctly', () {
      final dio = Dio();
      final config = RetryConfig.fromStrategy(RetryStrategy.noRetry);

      final interceptor = config.createInterceptor(dio);

      expect(interceptor, isNotNull);
      expect(interceptor, isA<RetryInterceptor>());
    });
  });

  group('RetryConfigExtension', () {
    test('should set retry strategy in request options', () {
      final options = RequestOptions(path: '/test');
      options.setRetryStrategy(RetryStrategy.fastStrategy);

      expect(options.extra['retryStrategy'], RetryStrategy.fastStrategy);
    });

    test('should get retry strategy from request options', () {
      final options = RequestOptions(path: '/test');
      options.extra['retryStrategy'] = RetryStrategy.conservativeStrategy;

      expect(options.retryStrategy, RetryStrategy.conservativeStrategy);
    });

    test('should return default strategy when not set', () {
      final options = RequestOptions(path: '/test');

      expect(options.retryStrategy, RetryStrategy.defaultStrategy);
    });

    test('should disable retry correctly', () {
      final options = RequestOptions(path: '/test');
      RetryConfigExtension(options).disableRetry();

      expect(options.extra['retryStrategy'], RetryStrategy.noRetry);
    });
  });
}
