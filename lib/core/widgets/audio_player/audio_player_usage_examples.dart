import 'package:flutter/material.dart';

import 'audio_player_controller.dart';
import 'audio_player_widget.dart';

/// Examples of how to use the AudioPlayerWidget
class AudioPlayerUsageExamples extends StatelessWidget {
  const AudioPlayerUsageExamples({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Audio Player Examples'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Example 1: Simple UI (original style)
            const Text(
              '1. Simple UI (Original Style)',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            const AudioPlayerWidget(
              id: 'simple_audio_1',
              audioPath: 'https://example.com/audio1.mp3',
              sourceType: AudioSourceType.remote,
              showProgress: true,
            ),

            const SizedBox(height: 24),

            // Example 2: Default UI (Voice message style)
            const Text(
              '2. Default UI (Voice Message Style)',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            const AudioPlayerWidget.defaultStyle(
              id: 'default_audio_1',
              audioPath: 'https://example.com/audio2.mp3',
              sourceType: AudioSourceType.remote,
            ),

            const SizedBox(height: 24),

            // Example 3: Custom styled default UI
            const Text(
              '3. Custom Styled Default UI',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            AudioPlayerWidget.defaultStyle(
              id: 'custom_default_audio_1',
              audioPath: 'https://example.com/audio3.mp3',
              sourceType: AudioSourceType.remote,
              backgroundColor: Colors.blue.withValues(alpha: 0.1),
              foregroundColor: Colors.blue,
              borderRadius: 16.0,
              padding: const EdgeInsets.all(16.0),
            ),

            const SizedBox(height: 24),

            // Example 4: Custom UI builder
            const Text(
              '4. Custom UI Builder',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            AudioPlayerWidget(
              id: 'custom_ui_audio_1',
              audioPath: 'https://example.com/audio4.mp3',
              sourceType: AudioSourceType.remote,
              customUIBuilder:
                  (context, state, isCurrentAudio, isPlaying, onPlayPause) {
                return Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Colors.purple.withValues(alpha: 0.2),
                        Colors.pink.withValues(alpha: 0.2)
                      ],
                    ),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      IconButton(
                        onPressed: onPlayPause,
                        icon: Icon(
                          isPlaying ? Icons.pause_circle : Icons.play_circle,
                          size: 40,
                          color: Colors.purple,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            'Custom Audio Player',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.purple.shade700,
                            ),
                          ),
                          if (isCurrentAudio && state.duration != null)
                            Text(
                              '${_formatDuration(state.position ?? Duration.zero)} / ${_formatDuration(state.duration!)}',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.purple.shade500,
                              ),
                            ),
                        ],
                      ),
                    ],
                  ),
                );
              },
            ),

            const SizedBox(height: 24),

            // Example 5: Simple UI with custom icons
            const Text(
              '5. Simple UI with Custom Icons',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            const AudioPlayerWidget(
              id: 'custom_icons_audio_1',
              audioPath: 'https://example.com/audio5.mp3',
              sourceType: AudioSourceType.remote,
              showProgress: true,
              playIcon:
                  Icon(Icons.play_circle_filled, color: Colors.green, size: 32),
              pauseIcon:
                  Icon(Icons.pause_circle_filled, color: Colors.red, size: 32),
              loadingWidget: CircularProgressIndicator(color: Colors.orange),
            ),
          ],
        ),
      ),
    );
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }
}
