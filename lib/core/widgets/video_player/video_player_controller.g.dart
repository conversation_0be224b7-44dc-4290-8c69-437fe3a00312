// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'video_player_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$videoPlayerControllerHash() =>
    r'2826bcff22448f4ff717db200965711767fec042';

/// See also [VideoPlayerController].
@ProviderFor(VideoPlayerController)
final videoPlayerControllerProvider = AutoDisposeNotifierProvider<
    VideoPlayerController, VideoPlayerState>.internal(
  VideoPlayerController.new,
  name: r'videoPlayerControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$videoPlayerControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$VideoPlayerController = AutoDisposeNotifier<VideoPlayerState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
