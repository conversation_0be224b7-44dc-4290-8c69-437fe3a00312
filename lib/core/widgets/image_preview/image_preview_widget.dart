import 'dart:io';

import 'package:dismissible_page/dismissible_page.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_audio_room/core/extensions/context_ext.dart';
import 'package:flutter_audio_room/core/extensions/navigator_ext.dart';
import 'package:flutter_audio_room/core/widgets/image_widget.dart';
import 'package:photo_view/photo_view.dart';

/// A widget that displays an image with preview capabilities.
/// Supports zooming, panning, and gesture-based dismissal.
class ImagePreviewWidget extends StatefulWidget {
  // Image source and path
  final ImageSource source;
  final String imagePath;

  // Hero animation tag
  final Object? tag;

  // Optional parameters for customization
  final double? initialScale;
  final double? minScale;
  final double? maxScale;
  final BoxFit? fit;
  final Color? backgroundColor;
  final Widget? loadingWidget;
  final Widget? errorWidget;
  final VoidCallback? onDismiss;

  const ImagePreviewWidget({
    super.key,
    required this.source,
    required this.imagePath,
    this.tag,
    this.initialScale,
    this.minScale,
    this.maxScale,
    this.fit = BoxFit.contain,
    this.backgroundColor = Colors.black,
    this.loadingWidget,
    this.errorWidget,
    this.onDismiss,
  });

  // Named constructors for different sources
  factory ImagePreviewWidget.network({
    Key? key,
    required String url,
    Object? tag,
    double? initialScale,
    double? minScale,
    double? maxScale,
    BoxFit? fit,
    Color? backgroundColor,
    Widget? loadingWidget,
    Widget? errorWidget,
    VoidCallback? onDismiss,
  }) {
    return ImagePreviewWidget(
      key: key,
      source: ImageSource.network,
      imagePath: url,
      tag: tag,
      initialScale: initialScale,
      minScale: minScale,
      maxScale: maxScale,
      fit: fit,
      backgroundColor: backgroundColor,
      loadingWidget: loadingWidget,
      errorWidget: errorWidget,
      onDismiss: onDismiss,
    );
  }

  factory ImagePreviewWidget.asset({
    Key? key,
    required String name,
    Object? tag,
    double? initialScale,
    double? minScale,
    double? maxScale,
    BoxFit? fit,
    Color? backgroundColor,
    Widget? loadingWidget,
    Widget? errorWidget,
    VoidCallback? onDismiss,
  }) {
    return ImagePreviewWidget(
      key: key,
      source: ImageSource.asset,
      imagePath: name,
      tag: tag,
      initialScale: initialScale,
      minScale: minScale,
      maxScale: maxScale,
      fit: fit,
      backgroundColor: backgroundColor,
      loadingWidget: loadingWidget,
      errorWidget: errorWidget,
      onDismiss: onDismiss,
    );
  }

  factory ImagePreviewWidget.file({
    Key? key,
    required String path,
    Object? tag,
    double? initialScale,
    double? minScale,
    double? maxScale,
    BoxFit? fit,
    Color? backgroundColor,
    Widget? loadingWidget,
    Widget? errorWidget,
    VoidCallback? onDismiss,
  }) {
    return ImagePreviewWidget(
      key: key,
      source: ImageSource.file,
      imagePath: path,
      tag: tag,
      initialScale: initialScale,
      minScale: minScale,
      maxScale: maxScale,
      fit: fit,
      backgroundColor: backgroundColor,
      loadingWidget: loadingWidget,
      errorWidget: errorWidget,
      onDismiss: onDismiss,
    );
  }

  @override
  State<ImagePreviewWidget> createState() => _ImagePreviewWidgetState();
}

class _ImagePreviewWidgetState extends State<ImagePreviewWidget> {
  bool showOverlay = true;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final photoView = PhotoView(
      imageProvider: _getImageProvider(),
      initialScale: widget.initialScale ?? PhotoViewComputedScale.contained,
      minScale: widget.minScale ?? PhotoViewComputedScale.contained * 0.8,
      maxScale: widget.maxScale ?? PhotoViewComputedScale.covered * 2,
      heroAttributes: widget.tag != null
          ? PhotoViewHeroAttributes(
              tag: widget.tag.toString(),
              transitionOnUserGestures: true,
            )
          : null,
      loadingBuilder: (context, event) => Center(
        child: widget.loadingWidget ??
            CircularProgressIndicator(
              value: event?.expectedTotalBytes != null
                  ? event!.cumulativeBytesLoaded / event.expectedTotalBytes!
                  : null,
            ),
      ),
      errorBuilder: (context, error, stackTrace) =>
          widget.errorWidget ??
          const Center(
            child: Icon(
              Icons.error_outline,
              color: Colors.white,
              size: 32,
            ),
          ),
    );

    final child = Stack(
      fit: StackFit.expand,
      children: [
        Positioned.fill(
          child: DismissiblePage(
            onDismissed: widget.onDismiss ??
            () {
              context.pop();
            },
            onDragStart: () {
              setState(() => showOverlay = false);
            },
            onDragEnd: () {
              setState(() => showOverlay = true);
            },
            direction: DismissiblePageDismissDirection.vertical,
            backgroundColor: widget.backgroundColor ?? Colors.black,
            child: GestureDetector(
              child: photoView,
              onTap: () => setState(() => showOverlay = !showOverlay),
            ),
          ),
        ),
        Positioned(
          top: 0,
          left: 0,
          right: 0,
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 400),
            decoration: BoxDecoration(
              color:
                  widget.backgroundColor ?? Colors.black.withValues(alpha: 0.6),
            ),
            padding: EdgeInsets.only(top: context.mediaQuery.viewPadding.top),
            child: showOverlay
                ? Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      IconButton(
                        onPressed: () => context.pop(),
                        icon: const Icon(CupertinoIcons.back),
                      ),
                    ],
                  )
                : null,
          ),
        )
      ],
    );

    return child;
  }

  ImageProvider _getImageProvider() {
    switch (widget.source) {
      case ImageSource.network:
        return NetworkImage(widget.imagePath);
      case ImageSource.asset:
        return AssetImage(widget.imagePath);
      case ImageSource.file:
        return FileImage(File(widget.imagePath));
    }
  }
}

/// A helper class to show image preview in a modal
class ImagePreviewModal {
  static Future<void> show({
    required BuildContext context,
    required ImageSource source,
    required String imagePath,
    Object? tag,
    double? initialScale,
    double? minScale,
    double? maxScale,
    BoxFit? fit,
    Color? backgroundColor,
    Widget? loadingWidget,
    Widget? errorWidget,
  }) {
    return Navigator.of(context).push(
      PageRouteBuilder(
        opaque: false,
        pageBuilder: (context, animation, secondaryAnimation) {
          return ImagePreviewWidget(
            source: source,
            imagePath: imagePath,
            tag: tag,
            initialScale: initialScale,
            minScale: minScale,
            maxScale: maxScale,
            fit: fit,
            backgroundColor: backgroundColor,
            loadingWidget: loadingWidget,
            errorWidget: errorWidget,
          );
        },
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return FadeTransition(
            opacity: animation,
            child: child,
          );
        },
      ),
    );
  }
}
