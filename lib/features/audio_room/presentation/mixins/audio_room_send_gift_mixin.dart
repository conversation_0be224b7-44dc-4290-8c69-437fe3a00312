import 'package:flutter_audio_room/core/utils/analytics_utils.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/leader_board_type.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/mixins/audio_room_provider_base_mixin.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/mixins/audio_room_provider_metadata_mixin.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/providers/unified_message_provider.dart';
import 'package:flutter_audio_room/features/authentication/data/model/avatar_frame_model.dart';
import 'package:flutter_audio_room/features/authentication/presentation/providers/auth_providers.dart';
import 'package:flutter_audio_room/services/gift_service/data/model/bag_gift_model.dart';
import 'package:flutter_audio_room/services/gift_service/data/model/gift_model.dart';
import 'package:flutter_audio_room/services/gift_service/data/model/gift_scene.dart';
import 'package:flutter_audio_room/shared/domain/models/either.dart';
import 'package:flutter_audio_room/shared/domain/types/common_types.dart';
import 'package:flutter_audio_room/shared/exceptions/error_handler.dart';

mixin AudioRoomSendGiftMixin
    on AudioRoomProviderBaseMixin, AudioRoomProviderMetadataMixin {
  Future<VoidResult> sendGift({
    required GiftModel gift,
    required List<String> selectedUserIds,
  }) async {
    if (state.currentRoom == null) {
      return Left(ErrorHandler.createValidationError('Room not found'));
    }

    if (selectedUserIds.isEmpty) {
      return Left(ErrorHandler.createValidationError(
          'Please select at least one user'));
    }

    final result = await giftRepository.sendGift(
      scene: GiftScene.room,
      roomId: state.currentRoom?.id ?? '',
      recipientUserIds: selectedUserIds,
      giftId: gift.id ?? 0,
    );

    return result.fold((l) => Left(l), (right) async {
      final recipients = state.members.values
          .where((e) => selectedUserIds.contains(e.userId))
          .toList();
      final message = gift.fetchGiftMessages(
        roomInfo: state.currentRoom!,
        sender: state.currentUser!,
        recipients: recipients,
        sendGiftResults: right,
      );
      ref.read(unifiedMessageProvider.notifier).addRoomMessage(message);
      audioService.sendChannelMessage(
          message,
      );

      // Track gift purchase/send
      await AnalyticsUtils.trackVirtualItemPurchase(
        itemType: 'gift',
        itemName: gift.name ?? 'Unknown Gift',
        quantity: selectedUserIds.length,
        price: (gift.price ?? 0).toDouble(),
        currency: 'coins', // 假设使用金币
        transactionId: DateTime.now().millisecondsSinceEpoch.toString(),
        recipientId: selectedUserIds.length == 1 ? selectedUserIds.first : null,
      );

      // Track message sent for gift
      await AnalyticsUtils.trackMessageSent(
        roomId: state.currentRoom?.id ?? '',
        messageType: 'gift',
      );

      // 发送礼物成功后更新钱包余额
      await ref.read(accountProvider.notifier).getUserWallet();

      return const Right(null);
    });
  }

  Future<VoidResult> sendFrame({
    required GiftModel frame,
    required List<String> selectedUserIds,
  }) async {
    if (state.currentRoom == null) {
      return Left(ErrorHandler.createValidationError('Room not found'));
    }

    final result = await giftRepository.sendFrame(
      scene: GiftScene.room,
      roomId: state.currentRoom!.id!,
      recipientUserIds: selectedUserIds,
      frameId: frame.id ?? 0,
    );

    return result.fold((l) => Left(l), (right) async {
      final recipients = state.members.values
          .where((e) => selectedUserIds.contains(e.userId))
          .toList();
      final message = frame.fetchGiftMessages(
        roomInfo: state.currentRoom!,
        sender: state.currentUser!,
        recipients: recipients,
        sendGiftResults: right,
      );
      audioService.sendChannelMessage(
          message,
      );

      // 发送边框成功后更新钱包余额
      await ref.read(accountProvider.notifier).getUserWallet();

      return const Right(null);
    });
  }

  Future<VoidResult> sendBagGift({
    required BagGiftModel bagGift,
    required String recipientUserId,
  }) async {
    if (state.currentRoom == null) {
      return Left(ErrorHandler.createValidationError('Room not found'));
    }

    final result = await giftRepository.sendBagGift(
      scene: GiftScene.room,
      roomId: state.currentRoom!.id!,
      recipientUserId: recipientUserId,
      bagGiftId: bagGift.id ?? 0,
    );

    return result.fold((l) => Left(l), (right) async {
      final recipientUser =
          state.members.values.firstWhere((e) => e.userId == recipientUserId);
      final message = bagGift.fetchGiftMessages(
        roomInfo: state.currentRoom!,
        sender: state.currentUser!,
        recipients: [recipientUser],
      );
      audioService.sendChannelMessage(
        message,
      );

      // 发送背包礼物成功后更新钱包余额
      await ref.read(accountProvider.notifier).getUserWallet();

      return const Right(null);
    });
  }

  Future<VoidResult> activeFrame(BagGiftModel bagGift) async {
    return ErrorHandler.handle(
      action: () async {
        final result = await giftRepository.useAvatarFrame(
          giftBagId: bagGift.id ?? -1,
        );
        return result.fold(
          (l) => Left(l),
          (r) async {
            final uid = state.currentUser?.uid;
            if (uid == null) {
              return Left(ErrorHandler.createValidationError('User not found'));
            }
            final gift = bagGift.giftMessage();
            final avatarFrame =
                AvatarFrameModel.fromJson(gift.toJson()).copyWith(
              giftStatus: 'active',
              activateExpireTime:
                  DateTime.now().add(Duration(days: gift.giftExpiryDays ?? 1)),
            );

            updateUserFrame(
              uid: uid,
              avatarFrame: avatarFrame,
            );

            await ref.read(accountProvider.notifier).updateUserFrame(
                  avatarFrame: avatarFrame,
                );
            return const Right(null);
          },
        );
      },
      identifier: 'active_frame',
    );
  }

  Future<VoidResult> updateUserFrame({
    required int uid,
    required AvatarFrameModel avatarFrame,
  }) async {
    var user = state.members[uid];
    if (user == null) {
      return Left(ErrorHandler.createValidationError('User not found'));
    }

    user = user.copyWith(avatarFrame: avatarFrame);

    final newState = state.copyWith(
      members: {
        ...state.members,
        uid: user,
      },
    );
    state = newState;

    if (uid == state.currentUser?.uid) {
      return await setUserMetadata(user);
    }
    return const Right(null);
  }

  /// Get gift rank data
  Future<VoidResult> getGiftRank() async {
    return ErrorHandler.handle(
      action: () async {
        return _fetchAndUpdateLeaderBoard(
          current: state.receivedLeaderBoard.current,
          size: state.receivedLeaderBoard.size,
          isRefresh: false,
        );
      },
      identifier: 'get_gift_rank',
    );
  }

  /// Refresh gift rank data from first page
  Future<VoidResult> refreshGiftRank() async {
    return ErrorHandler.handle(
      action: () async {
        return _fetchAndUpdateLeaderBoard(
          current: 0,
          size: state.receivedLeaderBoard.size,
          isRefresh: true,
        );
      },
      identifier: 'refresh_gift_rank',
    );
  }

  /// Fetch and update leader board data
  Future<VoidResult> _fetchAndUpdateLeaderBoard({
    required int current,
    required int size,
    required bool isRefresh,
  }) async {
    if (state.currentRoom?.id == null) {
      return Left(ErrorHandler.createValidationError('Room not found'));
    }

    final result = await repository.getLeaderBoard(
      current: current,
      size: size,
      roomId: state.currentRoom!.id!,
      leaderBoardType: LeaderBoardType.receiver,
    );

    return result.fold(
      (error) => Left(error),
      (leaderBoard) {
        state = state.copyWith(
          receivedLeaderBoard: isRefresh
              ? leaderBoard
              : state.receivedLeaderBoard.copyWith(
                  records: leaderBoard.records,
                ),
        );
        return const Right(null);
      },
    );
  }
}
