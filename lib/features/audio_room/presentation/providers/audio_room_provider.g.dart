// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'audio_room_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$audioRoomHash() => r'06efc238a5eb6d9675487561bb74e80bbfcd2976';

/// See also [AudioRoom].
@ProviderFor(AudioRoom)
final audioRoomProvider = NotifierProvider<AudioRoom, AudioRoomState>.internal(
  AudioRoom.new,
  name: r'audioRoomProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$audioRoomHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$AudioRoom = Notifier<AudioRoomState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
