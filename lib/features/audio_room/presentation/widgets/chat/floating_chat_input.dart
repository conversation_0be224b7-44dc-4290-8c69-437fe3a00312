import 'package:flutter/material.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/widgets/chat/chat_input_field.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 悬浮聊天输入框组件
/// 显示在键盘上方的聊天输入框
class FloatingChatInput extends StatelessWidget {
  /// 发送消息回调
  final Function(String message) onSendMessage;

  /// 是否启用输入框
  final bool enabled;

  /// 提示文本
  final String? hintText;

  /// 最大输入长度
  final int maxLength;

  /// 关闭回调
  final VoidCallback? onClose;

  const FloatingChatInput({
    super.key,
    required this.onSendMessage,
    this.enabled = true,
    this.hintText,
    this.maxLength = 200,
    this.onClose,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: const Color(0xFF262626),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.2),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.symmetric(
          horizontal: 16.w,
          vertical: 12.h,
        ),
        child: ChatInputField(
          onSendMessage: onSendMessage,
          enabled: enabled,
          hintText: hintText,
          maxLength: maxLength,
          autofocus: true,
          padding: EdgeInsets.symmetric(
            horizontal: 12.w,
            vertical: 8.h,
          ),
        ),
      ),
    );
  }
}
