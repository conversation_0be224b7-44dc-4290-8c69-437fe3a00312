// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'profile_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ProfileModelImpl _$$ProfileModelImplFromJson(Map<String, dynamic> json) =>
    _$ProfileModelImpl(
      id: json['id'] as String?,
      nickName: json['nickName'] as String?,
      role: json['role'] as String?,
      avatar: json['avatar'] as String?,
      gender: $enumDecodeNullable(_$UserGenderEnumMap, json['gender']),
      profileMessage: json['profileMessage'] as String?,
      birthday: json['birthday'] == null
          ? null
          : DateTime.parse(json['birthday'] as String),
      banned: json['banned'] as bool?,
      deleteStatus:
          $enumDecodeNullable(_$DeleteStatusEnumMap, json['deleteStatus']),
    );

Map<String, dynamic> _$$ProfileModelImplToJson(_$ProfileModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'nickName': instance.nickName,
      'role': instance.role,
      'avatar': instance.avatar,
      'gender': instance.gender?.toJson(),
      'profileMessage': instance.profileMessage,
      'birthday': instance.birthday?.toIso8601String(),
      'banned': instance.banned,
      'deleteStatus': instance.deleteStatus?.toJson(),
    };

const _$UserGenderEnumMap = {
  UserGender.male: 1,
  UserGender.female: 2,
  UserGender.other: 0,
};

const _$DeleteStatusEnumMap = {
  DeleteStatus.none: 0,
  DeleteStatus.pending: 1,
  DeleteStatus.deleted: 2,
};
