// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'follow_user_info_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$FollowUserInfoModelImpl _$$FollowUserInfoModelImplFromJson(
        Map<String, dynamic> json) =>
    _$FollowUserInfoModelImpl(
      profile: json['profileVO'] == null
          ? null
          : ProfileModel.fromJson(json['profileVO'] as Map<String, dynamic>),
      frame: json['avatarFrameInfo'] == null
          ? null
          : AvatarFrameModel.fromJson(
              json['avatarFrameInfo'] as Map<String, dynamic>),
      isFollower: json['isFollower'] as bool? ?? false,
      isFollowing: json['isFollowing'] as bool? ?? false,
    );

Map<String, dynamic> _$$FollowUserInfoModelImplToJson(
        _$FollowUserInfoModelImpl instance) =>
    <String, dynamic>{
      'profileVO': instance.profile?.toJson(),
      'avatarFrameInfo': instance.frame?.toJson(),
      'isFollower': instance.isFollower,
      'isFollowing': instance.isFollowing,
    };
