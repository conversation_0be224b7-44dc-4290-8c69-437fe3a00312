import 'package:dio/dio.dart';
import 'package:dio_smart_retry/dio_smart_retry.dart';
import 'package:flutter_audio_room/core/utils/log_utils.dart';
import 'package:flutter_audio_room/shared/constants/business_error_codes.dart';

/// 重试策略枚举
enum RetryStrategy {
  /// 默认策略：3次重试，指数退避
  defaultStrategy,

  /// 快速策略：2次重试，较短间隔
  fastStrategy,

  /// 保守策略：5次重试，较长间隔
  conservativeStrategy,

  /// 无重试
  noRetry,
}

/// 网络请求重试配置
class RetryConfig {
  /// 重试次数
  final int retries;

  /// 重试延迟列表
  final List<Duration> retryDelays;

  /// 额外的可重试状态码
  final Set<int> retryableExtraStatuses;

  /// 是否启用日志
  final bool enableLogging;

  const RetryConfig({
    required this.retries,
    required this.retryDelays,
    this.retryableExtraStatuses = const {},
    this.enableLogging = false,
  });

  /// 根据策略创建重试配置
  factory RetryConfig.fromStrategy(RetryStrategy strategy) {
    switch (strategy) {
      case RetryStrategy.defaultStrategy:
        return const RetryConfig(
          retries: 3,
          retryDelays: [
            Duration(seconds: 1),
            Duration(seconds: 2),
            Duration(seconds: 4),
          ],
          enableLogging: true,
        );

      case RetryStrategy.fastStrategy:
        return const RetryConfig(
          retries: 2,
          retryDelays: [
            Duration(milliseconds: 500),
            Duration(seconds: 1),
          ],
          enableLogging: true,
        );

      case RetryStrategy.conservativeStrategy:
        return const RetryConfig(
          retries: 5,
          retryDelays: [
            Duration(seconds: 1),
            Duration(seconds: 2),
            Duration(seconds: 4),
            Duration(seconds: 8),
            Duration(seconds: 16),
          ],
          enableLogging: true,
        );

      case RetryStrategy.noRetry:
        return const RetryConfig(
          retries: 0,
          retryDelays: [],
          enableLogging: false,
        );
    }
  }

  /// 创建RetryInterceptor
  RetryInterceptor createInterceptor(Dio dio) {
    if (retries == 0) {
      // 如果不需要重试，返回一个空的拦截器
      return RetryInterceptor(
        dio: dio,
        retries: 0,
        retryDelays: const [],
        logPrint: enableLogging ? print : null,
      );
    }

    return RetryInterceptor(
      dio: dio,
      retries: retries,
      retryDelays: retryDelays,
      retryableExtraStatuses: retryableExtraStatuses,
      logPrint: enableLogging ? _logPrint : null,
      retryEvaluator: _customRetryEvaluator,
    );
  }

  /// 自定义重试评估器
  /// 排除不应该重试的业务逻辑错误
  static bool _customRetryEvaluator(DioException error, int attempt) {
    // 检查是否是不应该重试的状态码
    final statusCode = error.response?.statusCode;
    if (statusCode != null) {
      // 不重试的状态码：
      // 400 - 客户端错误（参数错误等）
      // 401 - 未授权（需要重新登录）
      // 403 - 禁止访问（账户被限制）
      // 409 - 冲突（账户在其他设备登录）
      // 422 - 不可处理的实体（验证错误）
      final nonRetryableStatusCodes = {400, 401, 403, 409, 422};
      if (nonRetryableStatusCodes.contains(statusCode)) {
        return false;
      }
    }

    // 检查是否是业务逻辑错误码
    if (error.response?.data is Map<String, dynamic>) {
      final responseData = error.response!.data as Map<String, dynamic>;
      final code = responseData['code'] as String?;

      // 使用统一的业务错误码常量
      if (code != null && BusinessErrorCodes.isNonRetryableBusinessCode(code)) {
        return false;
      }
    }

    // 对于其他情况，检查是否是可重试的错误类型
    return _isRetryableError(error);
  }

  /// 检查是否是可重试的错误
  static bool _isRetryableError(DioException error) {
    // 网络连接错误
    if (error.type == DioExceptionType.connectionTimeout ||
        error.type == DioExceptionType.receiveTimeout ||
        error.type == DioExceptionType.sendTimeout ||
        error.type == DioExceptionType.connectionError) {
      return true;
    }

    // 服务器错误（5xx）
    final statusCode = error.response?.statusCode;
    if (statusCode != null && statusCode >= 500) {
      return true;
    }

    // 429 Too Many Requests
    if (statusCode == 429) {
      return true;
    }

    return false;
  }

  /// 自定义日志打印函数
  static void _logPrint(String message) {
    LogUtils.d(message, tag: 'RetryInterceptor');
  }
}

/// 重试配置扩展，用于在请求选项中设置重试策略
extension RetryConfigExtension on RequestOptions {
  /// 设置重试策略
  void setRetryStrategy(RetryStrategy strategy) {
    extra['retryStrategy'] = strategy;
  }

  /// 获取重试策略
  RetryStrategy get retryStrategy {
    final strategy = extra['retryStrategy'];
    if (strategy is RetryStrategy) {
      return strategy;
    }
    return RetryStrategy.defaultStrategy;
  }

  /// 禁用重试
  void disableRetry() {
    extra['retryStrategy'] = RetryStrategy.noRetry;
  }
}
