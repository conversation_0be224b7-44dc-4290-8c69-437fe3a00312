import 'package:dio/dio.dart';
import 'package:flutter_audio_room/shared/constants/business_error_codes.dart';
import 'package:flutter_audio_room/shared/data/remote/retry_config.dart';

/// 网络请求重试机制使用示例
///
/// 本文件展示了如何在不同场景下使用重试机制
class RetryUsageExample {
  /// 示例1：使用默认重试策略
  ///
  /// 默认策略：3次重试，指数退避（1秒、2秒、4秒）
  /// 适用于大多数API请求
  static void defaultRetryExample() {
    // 在DioNetworkService中已经默认启用
    // 无需额外配置，所有请求都会自动重试
  }

  /// 示例2：为特定请求禁用重试
  ///
  /// 适用于：
  /// - 登录请求（避免账户锁定）
  /// - 支付请求（避免重复扣费）
  /// - 敏感操作（避免重复执行）
  static Future<void> disableRetryExample(Dio dio) async {
    try {
      final options = Options();
      options.extra ??= {};
      options.extra!['retryStrategy'] = RetryStrategy.noRetry;

      await dio.post(
        '/auth/login',
        data: {'username': 'user', 'password': 'pass'},
        options: options,
      );
    } catch (e) {
      // 处理错误，不会重试
    }
  }

  /// 示例3：使用快速重试策略
  ///
  /// 快速策略：2次重试，较短间隔（0.5秒、1秒）
  /// 适用于：
  /// - 实时性要求高的请求
  /// - 轻量级查询操作
  static Future<void> fastRetryExample(Dio dio) async {
    try {
      final options = Options();
      options.extra ??= {};
      options.extra!['retryStrategy'] = RetryStrategy.fastStrategy;

      await dio.get(
        '/api/quick-data',
        options: options,
      );
    } catch (e) {
      // 处理错误
    }
  }

  /// 示例4：使用保守重试策略
  ///
  /// 保守策略：5次重试，较长间隔（1、2、4、8、16秒）
  /// 适用于：
  /// - 重要的数据同步操作
  /// - 文件上传/下载
  /// - 批量处理任务
  static Future<void> conservativeRetryExample(Dio dio) async {
    try {
      final options = Options();
      options.extra ??= {};
      options.extra!['retryStrategy'] = RetryStrategy.conservativeStrategy;

      await dio.post(
        '/api/important-sync',
        data: {'syncData': 'large_dataset'},
        options: options,
      );
    } catch (e) {
      // 处理错误
    }
  }

  /// 示例5：文件上传重试
  ///
  /// 文件上传通常需要更多重试次数和更长间隔
  static Future<void> fileUploadRetryExample(Dio dio) async {
    try {
      final formData = FormData.fromMap({
        'file': await MultipartFile.fromFile('/path/to/file.jpg'),
        'description': 'Profile picture',
      });

      final options = Options();
      options.extra ??= {};
      options.extra!['retryStrategy'] = RetryStrategy.conservativeStrategy;

      await dio.post(
        '/api/upload',
        data: formData,
        options: options,
        onSendProgress: (sent, total) {
          // 使用日志框架而不是print
          // LogUtils.d('Upload progress: ${(sent / total * 100).toStringAsFixed(1)}%');
        },
      );
    } catch (e) {
      // 处理上传失败
    }
  }

  /// 示例6：自定义重试配置
  ///
  /// 如果需要更精细的控制，可以创建自定义配置
  static RetryConfig createCustomRetryConfig() {
    return const RetryConfig(
      retries: 4,
      retryDelays: [
        Duration(milliseconds: 800),
        Duration(seconds: 2),
        Duration(seconds: 5),
        Duration(seconds: 10),
      ],
      retryableExtraStatuses: {408, 423}, // 添加额外的可重试状态码
      enableLogging: true,
    );
  }

  /// 示例7：业务错误码处理
  ///
  /// 展示如何使用统一的业务错误码常量
  static void handleBusinessErrorExample(String errorCode) {
    // 检查错误码类型
    if (BusinessErrorCodes.isTokenRefreshCode(errorCode)) {
      // LogUtils.d('需要刷新Token: ${BusinessErrorCodes.getErrorDescription(errorCode)}');
    } else if (BusinessErrorCodes.isLogoutCode(errorCode)) {
      // LogUtils.d('需要退出登录: ${BusinessErrorCodes.getErrorDescription(errorCode)}');
    } else if (BusinessErrorCodes.isRestrictCode(errorCode)) {
      // LogUtils.d('账户受限: ${BusinessErrorCodes.getErrorDescription(errorCode)}');
    } else if (BusinessErrorCodes.isPunishmentCode(errorCode)) {
      // LogUtils.d('功能受限: ${BusinessErrorCodes.getErrorDescription(errorCode)}');
    } else if (BusinessErrorCodes.isNonRetryableBusinessCode(errorCode)) {
      // LogUtils.d('不可重试的业务错误: ${BusinessErrorCodes.getErrorDescription(errorCode)}');
    } else {
      // LogUtils.d('其他错误: $errorCode');
    }
  }
}

/// 重试机制配置指南
/// 
/// 1. 重试策略选择：
///    - defaultStrategy: 适用于大多数API请求
///    - fastStrategy: 适用于实时性要求高的请求
///    - conservativeStrategy: 适用于重要操作和文件传输
///    - noRetry: 适用于敏感操作（登录、支付等）
/// 
/// 2. 自动重试的错误类型：
///    - 网络超时（连接、接收、发送超时）
///    - 服务器错误（5xx状态码）
///    - 网络连接问题
///    - 429 Too Many Requests
/// 
/// 3. 不会重试的错误类型：
///    - 客户端错误（400、401、403、409、422）
///    - 业务逻辑错误（token过期、账户限制等）
///    - 用户取消的请求
/// 
/// 4. 重试间隔策略：
///    - 指数退避：避免对服务器造成过大压力
///    - 随机抖动：防止多个客户端同时重试
/// 
/// 5. 日志记录：
///    - 开发环境：启用详细日志
///    - 生产环境：可选择性启用
/// 
/// 6. 性能考虑：
///    - 重试次数不宜过多（建议3-5次）
///    - 重试间隔要合理（避免长时间阻塞）
///    - 对于大文件传输，考虑使用断点续传
/// 
/// 7. 错误处理：
///    - 重试失败后，应该有合适的降级策略
///    - 向用户提供清晰的错误信息
///    - 记录重试失败的详细日志用于分析
