plugins {
    id "com.android.application"
    // START: FlutterFire Configuration
    id 'com.google.gms.google-services'
    id 'com.google.firebase.crashlytics'
    // END: FlutterFire Configuration
    id "kotlin-android"
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id "dev.flutter.flutter-gradle-plugin"
}

def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

android {

    // ----- BEGIN flavorDimensions (autogenerated by flutter_flavorizr) -----
    flavorDimensions += "flavor-type"

    productFlavors {
        dev {
            dimension "flavor-type"
            applicationId "com.wd.e2ee.chat.staging"
            resValue "string", "app_name", "E2EE Chat DEV"
        }
        staging {
            dimension "flavor-type"
            applicationId "com.wd.e2ee.chat.staging"
            resValue "string", "app_name", "E2EE Chat STAGING"
        }
        prod {
            dimension "flavor-type"
            applicationId "com.wd.e2ee.chat"
            resValue "string", "app_name", "E2EE Chat PROD"
        }
    }

    // ----- END flavorDimensions (autogenerated by flutter_flavorizr) -----

    namespace = "com.example.flutter_test_1"
    compileSdk = 35
    ndkVersion = flutter.ndkVersion

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
        coreLibraryDesugaringEnabled true
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_1_8
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId = "com.example.flutter_test_1"
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        minSdk = 24
        targetSdk = 35
        versionCode = flutter.versionCode
        versionName = flutter.versionName
    }

    signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile file(keystoreProperties['storeFile'])
            storePassword keystoreProperties['storePassword']
        }
    }

    buildTypes {
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig = signingConfigs.release
        }
    }

    // fix duplicate files error for agora
    packagingOptions {
        pickFirst 'lib/**/libaosl.so'
        /// exclude agora extensions
        // Spatial Audio
        exclude 'lib/armeabi-v7a/libagora_spatial_audio_extension.so'
        exclude 'lib/arm64-v8a/libagora_spatial_audio_extension.so'
        exclude 'lib/x86/libagora_spatial_audio_extension.so'
        exclude 'lib/x86_64/libagora_spatial_audio_extension.so'
        // Audio Beauty
        exclude 'lib/arm64-v8a/libagora_audio_beauty_extension.so'
        exclude 'lib/armeabi-v7a/libagora_audio_beauty_extension.so'
        exclude 'lib/x86/libagora_audio_beauty_extension.so'
        exclude 'lib/x86_64/libagora_audio_beauty_extension.so'
        // Clear Vision
        exclude 'lib/arm64-v8a/libagora_clear_vision_extension.so'
        exclude 'lib/armeabi-v7a/libagora_clear_vision_extension.so'
        exclude 'lib/x86/libagora_clear_vision_extension.so'
        exclude 'lib/x86_64/libagora_clear_vision_extension.so'
        // Perceptual Video Coding
        exclude 'lib/arm64-v8a/libagora_pvc_extension.so'
        exclude 'lib/armeabi-v7a/libagora_pvc_extension.so'
        // Virtual Background
        exclude 'lib/arm64-v8a/libagora_segmentation_extension.so'
        exclude 'lib/armeabi-v7a/libagora_segmentation_extension.so'
        // Copyright Music
        exclude 'lib/arm64-v8a/libagora_drm_loader_extension.so'
        exclude 'lib/arm64-v8a/libagora_udrm3_extension.so'
        exclude 'lib/armeabi-v7a/libagora_drm_loader_extension.so'
        exclude 'lib/armeabi-v7a/libagora_udrm3_extension.so'
        exclude 'lib/x86/libagora_drm_loader_extension.so'
        exclude 'lib/x86/libagora_udrm3_extension.so'
        exclude 'lib/x86_64/libagora_drm_loader_extension.so'
        exclude 'lib/x86_64/libagora_udrm3_extension.so'
        // Face Detection 
        exclude 'lib/arm64-v8a/libagora_face_detection_extension.so'
        exclude 'lib/armeabi-v7a/libagora_face_detection_extension.so'
        exclude 'lib/x86/libagora_face_detection_extension.so'
        exclude 'lib/x86_64/libagora_face_detection_extension.so'
        // Face Capture
        exclude 'lib/arm64-v8a/libagora_face_capture_extension.so'
        exclude 'lib/armeabi-v7a/libagora_face_capture_extension.so'
        // Super Resolution
        exclude 'lib/arm64-v8a/libagora_super_resolution_extension.so'
        exclude 'lib/armeabi-v7a/libagora_super_resolution_extension.so'
        // Screen Sharing
        exclude 'lib/arm64/libagora_screen_sharing_extension.so'
        exclude 'lib/armv7/libagora_screen_sharing_extension.so'
        exclude 'lib/arm64 & armv7/AgoraScreenShareExtension.aar'
        // Video Quality Analyzer
        exclude 'lib/arm64/libagora_video_quality_analyzer_extension.so'
        exclude 'lib/armv7/libagora_video_quality_analyzer_extension.so'
        exclude 'lib/x86/libagora_video_quality_analyzer_extension.so'
        exclude 'lib/x86_64/libagora_video_quality_analyzer_extension.so'
        // Video Encoding
        exclude 'lib/arm64/libagora_video_encoder_extension.so'
        exclude 'lib/arm64/video_enc.so'
        exclude 'lib/armv7/libagora_video_encoder_extension.so'
        exclude 'lib/armv7/video_enc.so'
        exclude 'lib/x86/libagora_video_encoder_extension.so'
        exclude 'lib/x86/video_enc.so'
        exclude 'lib/x86_64/libagora_video_encoder_extension.so'
        exclude 'lib/x86_64/video_enc.so'
        // Video Decoding
        exclude 'lib/arm64/libagora_video_decoder_extension.so'
        exclude 'lib/arm64/video_dec.so'
        exclude 'lib/armv7/libagora_video_decoder_extension.so'
        exclude 'lib/armv7/video_dec.so'
        exclude 'lib/x86/libagora_video_decoder_extension.so'
        exclude 'lib/x86/video_dec.so'
        exclude 'lib/x86_64/libagora_video_decoder_extension.so'
        exclude 'lib/x86_64/video_dec.so'
        // AV1 Stream Encoding  AV1
        exclude 'lib/arm64/libagora_video_av1_encoder_extension.so'
        exclude 'lib/armv7/libagora_video_av1_encoder_extension.so'
        exclude 'lib/x86/libagora_video_av1_encoder_extension.so'
        exclude 'lib/x86_64/libagora_video_av1_encoder_extension.so'
        // AV1 Stream Decoding  AV1
        exclude 'lib/arm64/libagora_video_av1_decoder_extension.so'
        exclude 'lib/armv7/libagora_video_av1_decoder_extension.so'
        exclude 'lib/x86/libagora_video_av1_decoder_extension.so'
        exclude 'lib/x86_64/libagora_video_av1_decoder_extension.so'
    }
}

flutter {
    source = "../.."
}

dependencies {
    def billing_version = "7.0.0"

    implementation "com.android.billingclient:billing:$billing_version"
    implementation 'com.github.prongbang:screen-protector:1.0.1'
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.1.4'
    api 'io.agora.infra:aosl:1.2.13'
}
